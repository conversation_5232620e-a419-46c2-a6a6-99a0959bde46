import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>dingUp, Loader2, Users } from "lucide-react";
import { CartesianGrid, Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis, Area, AreaChart } from "recharts";
import { IChartData } from "../../types/dashboard.types";
import { useResponsiveCharts } from "../../hooks/use-responsive-charts";
import React from "react";

interface IClientsChartProps {
	data: IChartData[];
	isLoading?: boolean;
}

const CustomTooltip = ({ active, payload, label }: any) => {
	if (active && payload && payload.length) {
		const value = payload[0].value;

		return (
			<div className="bg-white/95 backdrop-blur-sm p-4 border border-gray-200/50 rounded-xl shadow-xl">
				<p className="text-sm font-semibold text-gray-900 mb-2">{label}</p>
				<div className="flex items-center gap-2">
					<div className="w-3 h-3 rounded-full bg-gradient-to-r from-emerald-500 to-emerald-600"></div>
					<p className="text-sm font-medium text-emerald-600">
						{value} {value === 1 ? "cliente" : "clientes"}
					</p>
				</div>
			</div>
		);
	}
	return null;
};

const EmptyState = () => (
	<div className="flex flex-col items-center justify-center h-full text-gray-500">
		<Users size={48} className="mb-3 opacity-50" />
		<p className="text-sm font-medium">Nenhum cliente encontrado</p>
		<p className="text-xs text-gray-400">Dados de clientes aparecerão aqui</p>
	</div>
);

const LoadingState = () => (
	<div className="flex flex-col items-center justify-center h-full text-gray-500">
		<Loader2 size={32} className="animate-spin mb-3 text-emerald-500" />
		<p className="text-sm font-medium">Carregando clientes...</p>
	</div>
);

export const ClientsChart = React.memo(({ data, isLoading = false }: IClientsChartProps) => {
	const { isMobile, isTablet, chartHeight, margins, fontSize } = useResponsiveCharts();
	const hasData = data && data.length > 0;

	// Configurações aprimoradas para melhor visualização
	const enhancedMargins = {
		...margins,
		left: isMobile ? 20 : 30,
		right: isMobile ? 20 : 40,
		bottom: isMobile ? 50 : 60,
	};

	// Função para calcular intervalo dos ticks baseado na quantidade de dados
	const getTickInterval = () => {
		if (!data || data.length === 0) return 0;
		if (isMobile && data.length > 3) return Math.ceil(data.length / 2);
		if (isTablet && data.length > 4) return Math.ceil(data.length / 3);
		if (data.length > 5) return Math.ceil(data.length / 4); // Desktop com muitos itens
		return 0;
	};

	return (
		<div className="bg-gradient-to-br from-white via-gray-50/30 to-emerald-50/20 p-4 sm:p-6 rounded-2xl shadow-lg border border-gray-100/60 hover:shadow-xl transition-all duration-500 backdrop-blur-sm">
			<div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
				<div className="flex items-center gap-3">
					<div className="p-3 bg-gradient-to-br from-emerald-100 via-emerald-50 to-white rounded-xl shadow-md border border-emerald-100/50">
						<MapPin size={20} className="text-emerald-600 drop-shadow-sm" />
					</div>
					<div>
						<h3 className="font-bold text-gray-900 text-base sm:text-lg bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text">
							Clientes por Região
						</h3>
						<p className="text-xs sm:text-sm text-gray-500 font-medium">Distribuição geográfica dos clientes</p>
					</div>
				</div>
				<button className="flex items-center gap-2 text-xs sm:text-sm text-blue-600 hover:text-blue-700 transition-all duration-200 self-start sm:self-auto px-4 py-2 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100/50 border border-transparent hover:border-blue-200/50 shadow-sm hover:shadow-md">
					<TrendingUp size={14} className="drop-shadow-sm" />
					<span className="hidden sm:inline font-medium">Ver detalhes</span>
					<span className="sm:hidden font-medium">Detalhes</span>
				</button>
			</div>

			<div className="w-full transition-all duration-300" style={{ height: chartHeight }}>
				{isLoading ? (
					<LoadingState />
				) : !hasData ? (
					<EmptyState />
				) : (
					<ResponsiveContainer width="100%" height="100%">
						<AreaChart data={data} margin={enhancedMargins}>
							<defs>
								<linearGradient id="areaGradient" x1="0" y1="0" x2="0" y2="1">
									<stop offset="0%" stopColor="#10b981" stopOpacity={0.4} />
									<stop offset="50%" stopColor="#10b981" stopOpacity={0.2} />
									<stop offset="100%" stopColor="#10b981" stopOpacity={0.02} />
								</linearGradient>
								<linearGradient id="lineGradient" x1="0" y1="0" x2="1" y2="0">
									<stop offset="0%" stopColor="#047857" />
									<stop offset="30%" stopColor="#059669" />
									<stop offset="70%" stopColor="#10b981" />
									<stop offset="100%" stopColor="#34d399" />
								</linearGradient>
								<filter id="glow">
									<feGaussianBlur stdDeviation="3" result="coloredBlur" />
									<feMerge>
										<feMergeNode in="coloredBlur" />
										<feMergeNode in="SourceGraphic" />
									</feMerge>
								</filter>
							</defs>
							<CartesianGrid strokeDasharray="2 4" stroke="#e2e8f0" strokeOpacity={0.4} vertical={false} horizontal={true} />
							<XAxis
								dataKey="name"
								axisLine={false}
								tickLine={false}
								tick={{
									fontSize: fontSize.tick,
									fill: "#64748b",
									fontWeight: 600,
								}}
								interval={getTickInterval()}
								angle={isMobile ? -45 : 0}
								textAnchor={isMobile ? "end" : "middle"}
								height={isMobile ? 70 : 50}
							/>
							<YAxis
								axisLine={false}
								tickLine={false}
								tick={{
									fontSize: fontSize.tick,
									fill: "#64748b",
									fontWeight: 600,
								}}
								width={isMobile ? 45 : 70}
								tickFormatter={value => {
									if (isMobile) {
										return value.toString();
									}
									return `${value}`;
								}}
								domain={["dataMin - 1", "dataMax + 1"]}
							/>
							<Tooltip content={<CustomTooltip />} />
							<Area
								type="monotone"
								dataKey="valor"
								stroke="url(#lineGradient)"
								fill="url(#areaGradient)"
								strokeWidth={3.5}
								dot={{
									fill: "#ffffff",
									stroke: "#10b981",
									strokeWidth: 3,
									r: isMobile ? 5 : 7,
									className: "drop-shadow-lg",
									filter: "url(#glow)",
								}}
								activeDot={{
									r: isMobile ? 8 : 10,
									stroke: "#059669",
									strokeWidth: 3,
									fill: "#ffffff",
									className: "drop-shadow-xl",
									filter: "url(#glow)",
								}}
								className="drop-shadow-md"
								connectNulls={false}
							/>
						</AreaChart>
					</ResponsiveContainer>
				)}
			</div>
		</div>
	);
});

ClientsChart.displayName = "ClientsChart";
