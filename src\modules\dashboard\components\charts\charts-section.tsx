import { CardLMPContainer } from "@/shared/components/custom/card";
import { BarChart3 } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { IDashboardChartsData, PeriodType } from "../../types/dashboard.types";
import { SalesChart } from "./sales-chart";
import { ProductsChart } from "./products-chart";
import { ClientsChart } from "./clients-chart";
import React from "react";

interface IChartsSectionProps {
	selectedPeriod: PeriodType;
	onPeriodChange: (period: PeriodType) => void;
	chartsData: IDashboardChartsData;
}

interface IPeriodButtonProps {
	period: PeriodType;
	label: string;
	isActive: boolean;
	onClick: (period: PeriodType) => void;
}

interface IPeriodSelectorProps {
	selectedPeriod: PeriodType;
	onPeriodChange: (period: PeriodType) => void;
}

const PeriodButton = React.memo(({ period, label, isActive, onClick }: IPeriodButtonProps) => (
	<button
		className={`
			px-3 py-1.5 font-medium text-xs sm:text-sm rounded-md transition-colors duration-150
			focus:outline-none focus:ring-1 focus:ring-mainColor/30
			${isActive ? "bg-mainColor text-white" : "text-gray-600 hover:text-gray-800 hover:bg-gray-50 bg-transparent"}
		`}
		onClick={() => onClick(period)}
		aria-pressed={isActive}
		role="tab"
	>
		{label}
	</button>
));

PeriodButton.displayName = "PeriodButton";

const CompactPeriodSelector = React.memo(({ selectedPeriod, onPeriodChange }: IPeriodSelectorProps) => {
	const periods: { period: PeriodType; label: string }[] = [
		{ period: "diario", label: "Diário" },
		{ period: "semanal", label: "Semanal" },
		{ period: "mensal", label: "Mensal" },
		{ period: "anual", label: "Anual" },
	];

	return (
		<div className="flex items-center gap-1 sm:gap-2">
			<span className="hidden sm:inline text-xs font-medium text-gray-600 mr-2">Período:</span>
			<div className="flex gap-1 p-1 bg-gray-50 rounded-lg border border-gray-200" role="tablist" aria-label="Seleção de período">
				{periods.map(({ period, label }) => (
					<PeriodButton key={period} period={period} label={label} isActive={selectedPeriod === period} onClick={onPeriodChange} />
				))}
			</div>
		</div>
	);
});

CompactPeriodSelector.displayName = "CompactPeriodSelector";

export const ChartsSection = React.memo(({ selectedPeriod, onPeriodChange, chartsData }: IChartsSectionProps) => {
	return (
		<CardLMPContainer
			icon={<BarChart3 size={22} className="text-mainColor" />}
			title="Análise de Dados"
			description="Visualize os dados do seu negócio em diferentes perspectivas"
			actions={<CompactPeriodSelector selectedPeriod={selectedPeriod} onPeriodChange={onPeriodChange} />}
		>
			<AnimatePresence mode="wait">
				<motion.div
					key={selectedPeriod}
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					exit={{ opacity: 0, y: -20 }}
					transition={{ duration: 0.4, ease: "easeOut" }}
					className="grid grid-cols-1 lg:grid-cols-2 gap-6"
				>
					<motion.div
						initial={{ opacity: 0, x: -30 }}
						animate={{ opacity: 1, x: 0 }}
						transition={{ duration: 0.4, delay: 0.1, ease: "easeOut" }}
						className="transform hover:scale-[1.02] transition-transform duration-200"
					>
						<SalesChart data={chartsData.vendasMensais} />
					</motion.div>
					<motion.div
						initial={{ opacity: 0, x: 30 }}
						animate={{ opacity: 1, x: 0 }}
						transition={{ duration: 0.4, delay: 0.2, ease: "easeOut" }}
						className="transform hover:scale-[1.02] transition-transform duration-200"
					>
						<ProductsChart data={chartsData.produtosMaisVendidos} />
					</motion.div>
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.4, delay: 0.3, ease: "easeOut" }}
						className="lg:col-span-2 transform hover:scale-[1.01] transition-transform duration-200"
					>
						<ClientsChart data={chartsData.clientesPorRegiao} />
					</motion.div>
				</motion.div>
			</AnimatePresence>
		</CardLMPContainer>
	);
});

ChartsSection.displayName = "ChartsSection";
